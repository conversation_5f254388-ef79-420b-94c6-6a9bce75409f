@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #ec4899;
  --primary-dark: #db2777;
  --secondary-color: #8b5cf6;
  --accent-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background: #ffffff;
  --surface: #f9fafb;
  --border: #e5e7eb;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
}

.btn-primary {
  @apply bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  @apply border-2 border-pink-500 text-pink-500 hover:bg-pink-500 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
}

.input-field {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent outline-none transition-all duration-200;
}

.gradient-text {
  @apply bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent;
}

.swipe-card {
  @apply relative bg-white rounded-2xl shadow-lg overflow-hidden cursor-grab active:cursor-grabbing;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.swipe-card:hover {
  transform: translateY(-4px);
}

.chat-bubble {
  @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl;
}

.chat-bubble.sent {
  @apply bg-pink-500 text-white ml-auto;
}

.chat-bubble.received {
  @apply bg-gray-200 text-gray-800;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}
